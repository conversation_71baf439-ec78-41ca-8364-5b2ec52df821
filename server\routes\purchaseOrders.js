import express from 'express';
import PurchaseOrder from '../models/PurchaseOrder.js';
import { authenticateToken, requireAdmin, requireTrucker, requireAdminOrTrucker } from '../middleware/auth.js';

const router = express.Router();

// Get all purchase orders (admin can see all, truckers see only pending and their assigned)
router.get('/', authenticateToken, async (req, res) => {
  try {
    let query = {};
    
    if (req.user.role === 'trucker') {
      // Truckers can see pending orders and orders assigned to them
      query = {
        $or: [
          { status: 'pending' },
          { assignedTo: req.user._id }
        ]
      };
    }
    // Admins can see all orders (no filter needed)

    const orders = await PurchaseOrder.find(query)
      .populate('createdBy', 'username firstName lastName')
      .populate('assignedTo', 'username firstName lastName')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: orders
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching purchase orders',
      error: error.message
    });
  }
});

// Get single purchase order
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const order = await PurchaseOrder.findById(req.params.id)
      .populate('createdBy', 'username firstName lastName')
      .populate('assignedTo', 'username firstName lastName');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    // Check permissions
    if (req.user.role === 'trucker' && 
        order.status !== 'pending' && 
        !order.assignedTo?.equals(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching purchase order',
      error: error.message
    });
  }
});

// Create new purchase order (admin only)
router.post('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const orderData = {
      ...req.body,
      createdBy: req.user._id
    };

    const order = new PurchaseOrder(orderData);
    await order.save();

    await order.populate('createdBy', 'username firstName lastName');

    res.status(201).json({
      success: true,
      message: 'Purchase order created successfully',
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error creating purchase order',
      error: error.message
    });
  }
});

// Accept purchase order (trucker only)
router.patch('/:id/accept', authenticateToken, requireTrucker, async (req, res) => {
  try {
    const order = await PurchaseOrder.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    if (order.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Purchase order is not available for acceptance'
      });
    }

    order.status = 'accepted';
    order.assignedTo = req.user._id;
    order.acceptedAt = new Date();

    await order.save();
    await order.populate('assignedTo', 'username firstName lastName');

    res.json({
      success: true,
      message: 'Purchase order accepted successfully',
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error accepting purchase order',
      error: error.message
    });
  }
});

// Update purchase order status (admin only)
router.patch('/:id/status', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { status } = req.body;
    const order = await PurchaseOrder.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    order.status = status;
    await order.save();

    await order.populate('createdBy', 'username firstName lastName');
    await order.populate('assignedTo', 'username firstName lastName');

    res.json({
      success: true,
      message: 'Purchase order status updated successfully',
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error updating purchase order status',
      error: error.message
    });
  }
});

// Delete purchase order (admin only)
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const order = await PurchaseOrder.findByIdAndDelete(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    res.json({
      success: true,
      message: 'Purchase order deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error deleting purchase order',
      error: error.message
    });
  }
});

export default router;
