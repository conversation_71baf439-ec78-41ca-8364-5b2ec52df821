import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';

interface PurchaseOrder {
  _id: string;
  orderNumber: string;
  title: string;
  description: string;
  pickupLocation: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
  };
  deliveryLocation: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
  };
  pickupDate: string;
  deliveryDate: string;
  weight: number;
  rate: number;
  status: string;
  createdBy: {
    username: string;
    firstName: string;
    lastName: string;
  };
  assignedTo?: {
    username: string;
    firstName: string;
    lastName: string;
  };
  acceptedAt?: string;
  createdAt: string;
}

const AdminDashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const [orders, setOrders] = useState<PurchaseOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    pickupAddress: '',
    pickupCity: '',
    pickupState: '',
    pickupZipCode: '',
    deliveryAddress: '',
    deliveryCity: '',
    deliveryState: '',
    deliveryZipCode: '',
    pickupDate: '',
    deliveryDate: '',
    weight: '',
    rate: '',
    notes: ''
  });

  const API_BASE_URL = 'http://localhost:5052/api';

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/purchase-orders`);
      if (response.data.success) {
        setOrders(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateOrder = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const orderData = {
        title: formData.title,
        description: formData.description,
        pickupLocation: {
          address: formData.pickupAddress,
          city: formData.pickupCity,
          state: formData.pickupState,
          zipCode: formData.pickupZipCode
        },
        deliveryLocation: {
          address: formData.deliveryAddress,
          city: formData.deliveryCity,
          state: formData.deliveryState,
          zipCode: formData.deliveryZipCode
        },
        pickupDate: formData.pickupDate,
        deliveryDate: formData.deliveryDate,
        weight: parseFloat(formData.weight),
        rate: parseFloat(formData.rate),
        notes: formData.notes
      };

      const response = await axios.post(`${API_BASE_URL}/purchase-orders`, orderData);
      if (response.data.success) {
        setOrders([response.data.data, ...orders]);
        setShowCreateForm(false);
        // Reset form
        setFormData({
          title: '',
          description: '',
          pickupAddress: '',
          pickupCity: '',
          pickupState: '',
          pickupZipCode: '',
          deliveryAddress: '',
          deliveryCity: '',
          deliveryState: '',
          deliveryZipCode: '',
          pickupDate: '',
          deliveryDate: '',
          weight: '',
          rate: '',
          notes: ''
        });
      }
    } catch (error) {
      console.error('Error creating order:', error);
      alert('Error creating purchase order');
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-warning';
      case 'accepted': return 'bg-success';
      case 'in_transit': return 'bg-info';
      case 'delivered': return 'bg-primary';
      case 'cancelled': return 'bg-danger';
      default: return 'bg-secondary';
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Header */}
      <nav className="navbar navbar-expand-lg navbar-dark bg-success mb-4">
        <div className="container-fluid">
          <span className="navbar-brand">
            <i className="bi bi-person-gear me-2"></i>
            Admin Dashboard
          </span>
          <div className="navbar-nav ms-auto">
            <span className="navbar-text me-3">
              Welcome, {user?.firstName} {user?.lastName}
            </span>
            <button className="btn btn-outline-light" onClick={logout}>
              <i className="bi bi-box-arrow-right me-1"></i>
              Logout
            </button>
          </div>
        </div>
      </nav>

      <div className="container-fluid">
        {/* Action Bar */}
        <div className="row mb-4">
          <div className="col">
            <div className="d-flex justify-content-between align-items-center">
              <h2>Purchase Orders</h2>
              <button
                className="btn btn-success"
                onClick={() => setShowCreateForm(true)}
              >
                <i className="bi bi-plus-circle me-2"></i>
                Create New Order
              </button>
            </div>
          </div>
        </div>

        {/* Statistics */}
        <div className="row mb-4">
          <div className="col-md-3">
            <div className="card bg-warning text-white">
              <div className="card-body">
                <h5>Pending Orders</h5>
                <h3>{orders.filter(o => o.status === 'pending').length}</h3>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card bg-success text-white">
              <div className="card-body">
                <h5>Accepted Orders</h5>
                <h3>{orders.filter(o => o.status === 'accepted').length}</h3>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card bg-info text-white">
              <div className="card-body">
                <h5>In Transit</h5>
                <h3>{orders.filter(o => o.status === 'in_transit').length}</h3>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card bg-primary text-white">
              <div className="card-body">
                <h5>Delivered</h5>
                <h3>{orders.filter(o => o.status === 'delivered').length}</h3>
              </div>
            </div>
          </div>
        </div>

        {/* Orders Table */}
        <div className="card">
          <div className="card-body">
            <div className="table-responsive">
              <table className="table table-striped">
                <thead>
                  <tr>
                    <th>Order #</th>
                    <th>Title</th>
                    <th>Pickup Location</th>
                    <th>Delivery Location</th>
                    <th>Status</th>
                    <th>Assigned To</th>
                    <th>Rate</th>
                    <th>Created</th>
                  </tr>
                </thead>
                <tbody>
                  {orders.map((order) => (
                    <tr key={order._id}>
                      <td>{order.orderNumber}</td>
                      <td>{order.title}</td>
                      <td>{order.pickupLocation.city}, {order.pickupLocation.state}</td>
                      <td>{order.deliveryLocation.city}, {order.deliveryLocation.state}</td>
                      <td>
                        <span className={`badge ${getStatusBadgeClass(order.status)}`}>
                          {order.status.replace('_', ' ').toUpperCase()}
                        </span>
                      </td>
                      <td>
                        {order.assignedTo ? 
                          `${order.assignedTo.firstName} ${order.assignedTo.lastName}` : 
                          'Unassigned'
                        }
                      </td>
                      <td>${order.rate.toLocaleString()}</td>
                      <td>{new Date(order.createdAt).toLocaleDateString()}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Create Order Modal */}
      {showCreateForm && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Create New Purchase Order</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowCreateForm(false)}
                ></button>
              </div>
              <form onSubmit={handleCreateOrder}>
                <div className="modal-body">
                  <div className="row">
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">Title</label>
                        <input
                          type="text"
                          className="form-control"
                          value={formData.title}
                          onChange={(e) => setFormData({...formData, title: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">Weight (lbs)</label>
                        <input
                          type="number"
                          className="form-control"
                          value={formData.weight}
                          onChange={(e) => setFormData({...formData, weight: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div className="mb-3">
                    <label className="form-label">Description</label>
                    <textarea
                      className="form-control"
                      rows={3}
                      value={formData.description}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                      required
                    ></textarea>
                  </div>

                  <h6>Pickup Location</h6>
                  <div className="row">
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">Address</label>
                        <input
                          type="text"
                          className="form-control"
                          value={formData.pickupAddress}
                          onChange={(e) => setFormData({...formData, pickupAddress: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-3">
                      <div className="mb-3">
                        <label className="form-label">City</label>
                        <input
                          type="text"
                          className="form-control"
                          value={formData.pickupCity}
                          onChange={(e) => setFormData({...formData, pickupCity: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-2">
                      <div className="mb-3">
                        <label className="form-label">State</label>
                        <input
                          type="text"
                          className="form-control"
                          value={formData.pickupState}
                          onChange={(e) => setFormData({...formData, pickupState: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-1">
                      <div className="mb-3">
                        <label className="form-label">ZIP</label>
                        <input
                          type="text"
                          className="form-control"
                          value={formData.pickupZipCode}
                          onChange={(e) => setFormData({...formData, pickupZipCode: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <h6>Delivery Location</h6>
                  <div className="row">
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">Address</label>
                        <input
                          type="text"
                          className="form-control"
                          value={formData.deliveryAddress}
                          onChange={(e) => setFormData({...formData, deliveryAddress: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-3">
                      <div className="mb-3">
                        <label className="form-label">City</label>
                        <input
                          type="text"
                          className="form-control"
                          value={formData.deliveryCity}
                          onChange={(e) => setFormData({...formData, deliveryCity: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-2">
                      <div className="mb-3">
                        <label className="form-label">State</label>
                        <input
                          type="text"
                          className="form-control"
                          value={formData.deliveryState}
                          onChange={(e) => setFormData({...formData, deliveryState: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-1">
                      <div className="mb-3">
                        <label className="form-label">ZIP</label>
                        <input
                          type="text"
                          className="form-control"
                          value={formData.deliveryZipCode}
                          onChange={(e) => setFormData({...formData, deliveryZipCode: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-md-4">
                      <div className="mb-3">
                        <label className="form-label">Pickup Date</label>
                        <input
                          type="date"
                          className="form-control"
                          value={formData.pickupDate}
                          onChange={(e) => setFormData({...formData, pickupDate: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-4">
                      <div className="mb-3">
                        <label className="form-label">Delivery Date</label>
                        <input
                          type="date"
                          className="form-control"
                          value={formData.deliveryDate}
                          onChange={(e) => setFormData({...formData, deliveryDate: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-4">
                      <div className="mb-3">
                        <label className="form-label">Rate ($)</label>
                        <input
                          type="number"
                          step="0.01"
                          className="form-control"
                          value={formData.rate}
                          onChange={(e) => setFormData({...formData, rate: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={() => setShowCreateForm(false)}
                  >
                    Cancel
                  </button>
                  <button type="submit" className="btn btn-success">
                    Create Order
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
