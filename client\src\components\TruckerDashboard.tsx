import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';

interface PurchaseOrder {
  _id: string;
  orderNumber: string;
  title: string;
  description: string;
  pickupLocation: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
  };
  deliveryLocation: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
  };
  pickupDate: string;
  deliveryDate: string;
  weight: number;
  rate: number;
  status: string;
  createdBy: {
    username: string;
    firstName: string;
    lastName: string;
  };
  assignedTo?: {
    username: string;
    firstName: string;
    lastName: string;
  };
  acceptedAt?: string;
  createdAt: string;
  notes?: string;
}

const TruckerDashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const [orders, setOrders] = useState<PurchaseOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<PurchaseOrder | null>(null);
  const [showModal, setShowModal] = useState(false);

  const API_BASE_URL = 'http://localhost:5050/api';

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/purchase-orders`);
      if (response.data.success) {
        setOrders(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAcceptOrder = async (orderId: string) => {
    try {
      const response = await axios.patch(`${API_BASE_URL}/purchase-orders/${orderId}/accept`);
      if (response.data.success) {
        // Update the orders list
        setOrders(orders.map(order => 
          order._id === orderId 
            ? { ...order, status: 'accepted', assignedTo: user, acceptedAt: new Date().toISOString() }
            : order
        ));
        setShowModal(false);
        setSelectedOrder(null);
      }
    } catch (error) {
      console.error('Error accepting order:', error);
      alert('Error accepting order');
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-warning';
      case 'accepted': return 'bg-success';
      case 'in_transit': return 'bg-info';
      case 'delivered': return 'bg-primary';
      case 'cancelled': return 'bg-danger';
      default: return 'bg-secondary';
    }
  };

  const pendingOrders = orders.filter(order => order.status === 'pending');
  const myOrders = orders.filter(order => order.assignedTo?._id === user?._id);

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Header */}
      <nav className="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
        <div className="container-fluid">
          <span className="navbar-brand">
            <i className="bi bi-truck me-2"></i>
            Trucker Dashboard
          </span>
          <div className="navbar-nav ms-auto">
            <span className="navbar-text me-3">
              Welcome, {user?.firstName} {user?.lastName}
            </span>
            <button className="btn btn-outline-light" onClick={logout}>
              <i className="bi bi-box-arrow-right me-1"></i>
              Logout
            </button>
          </div>
        </div>
      </nav>

      <div className="container-fluid">
        {/* Statistics */}
        <div className="row mb-4">
          <div className="col-md-4">
            <div className="card bg-warning text-white">
              <div className="card-body">
                <h5>Available Orders</h5>
                <h3>{pendingOrders.length}</h3>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card bg-success text-white">
              <div className="card-body">
                <h5>My Active Orders</h5>
                <h3>{myOrders.filter(o => o.status !== 'delivered').length}</h3>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card bg-primary text-white">
              <div className="card-body">
                <h5>Completed Orders</h5>
                <h3>{myOrders.filter(o => o.status === 'delivered').length}</h3>
              </div>
            </div>
          </div>
        </div>

        {/* Available Orders */}
        <div className="row mb-4">
          <div className="col">
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">
                  <i className="bi bi-list-ul me-2"></i>
                  Available Orders
                </h5>
              </div>
              <div className="card-body">
                {pendingOrders.length === 0 ? (
                  <p className="text-muted">No pending orders available.</p>
                ) : (
                  <div className="table-responsive">
                    <table className="table table-hover">
                      <thead>
                        <tr>
                          <th>Order #</th>
                          <th>Title</th>
                          <th>Route</th>
                          <th>Pickup Date</th>
                          <th>Rate</th>
                          <th>Weight</th>
                          <th>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {pendingOrders.map((order) => (
                          <tr key={order._id}>
                            <td>{order.orderNumber}</td>
                            <td>{order.title}</td>
                            <td>
                              {order.pickupLocation.city}, {order.pickupLocation.state} → {' '}
                              {order.deliveryLocation.city}, {order.deliveryLocation.state}
                            </td>
                            <td>{new Date(order.pickupDate).toLocaleDateString()}</td>
                            <td>${order.rate.toLocaleString()}</td>
                            <td>{order.weight.toLocaleString()} lbs</td>
                            <td>
                              <button
                                className="btn btn-primary btn-sm"
                                onClick={() => {
                                  setSelectedOrder(order);
                                  setShowModal(true);
                                }}
                              >
                                View Details
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* My Orders */}
        <div className="row">
          <div className="col">
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">
                  <i className="bi bi-truck me-2"></i>
                  My Orders
                </h5>
              </div>
              <div className="card-body">
                {myOrders.length === 0 ? (
                  <p className="text-muted">You haven't accepted any orders yet.</p>
                ) : (
                  <div className="table-responsive">
                    <table className="table table-hover">
                      <thead>
                        <tr>
                          <th>Order #</th>
                          <th>Title</th>
                          <th>Route</th>
                          <th>Status</th>
                          <th>Pickup Date</th>
                          <th>Delivery Date</th>
                          <th>Rate</th>
                        </tr>
                      </thead>
                      <tbody>
                        {myOrders.map((order) => (
                          <tr key={order._id}>
                            <td>{order.orderNumber}</td>
                            <td>{order.title}</td>
                            <td>
                              {order.pickupLocation.city}, {order.pickupLocation.state} → {' '}
                              {order.deliveryLocation.city}, {order.deliveryLocation.state}
                            </td>
                            <td>
                              <span className={`badge ${getStatusBadgeClass(order.status)}`}>
                                {order.status.replace('_', ' ').toUpperCase()}
                              </span>
                            </td>
                            <td>{new Date(order.pickupDate).toLocaleDateString()}</td>
                            <td>{new Date(order.deliveryDate).toLocaleDateString()}</td>
                            <td>${order.rate.toLocaleString()}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Order Details Modal */}
      {showModal && selectedOrder && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Order Details - {selectedOrder.orderNumber}</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowModal(false);
                    setSelectedOrder(null);
                  }}
                ></button>
              </div>
              <div className="modal-body">
                <div className="row">
                  <div className="col-md-6">
                    <h6>Order Information</h6>
                    <p><strong>Title:</strong> {selectedOrder.title}</p>
                    <p><strong>Description:</strong> {selectedOrder.description}</p>
                    <p><strong>Weight:</strong> {selectedOrder.weight.toLocaleString()} lbs</p>
                    <p><strong>Rate:</strong> ${selectedOrder.rate.toLocaleString()}</p>
                    {selectedOrder.notes && (
                      <p><strong>Notes:</strong> {selectedOrder.notes}</p>
                    )}
                  </div>
                  <div className="col-md-6">
                    <h6>Dates</h6>
                    <p><strong>Pickup Date:</strong> {new Date(selectedOrder.pickupDate).toLocaleDateString()}</p>
                    <p><strong>Delivery Date:</strong> {new Date(selectedOrder.deliveryDate).toLocaleDateString()}</p>
                    <p><strong>Created:</strong> {new Date(selectedOrder.createdAt).toLocaleDateString()}</p>
                  </div>
                </div>
                
                <div className="row mt-3">
                  <div className="col-md-6">
                    <h6>Pickup Location</h6>
                    <p>
                      {selectedOrder.pickupLocation.address}<br />
                      {selectedOrder.pickupLocation.city}, {selectedOrder.pickupLocation.state} {selectedOrder.pickupLocation.zipCode}
                    </p>
                  </div>
                  <div className="col-md-6">
                    <h6>Delivery Location</h6>
                    <p>
                      {selectedOrder.deliveryLocation.address}<br />
                      {selectedOrder.deliveryLocation.city}, {selectedOrder.deliveryLocation.state} {selectedOrder.deliveryLocation.zipCode}
                    </p>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowModal(false);
                    setSelectedOrder(null);
                  }}
                >
                  Close
                </button>
                {selectedOrder.status === 'pending' && (
                  <button
                    type="button"
                    className="btn btn-success"
                    onClick={() => handleAcceptOrder(selectedOrder._id)}
                  >
                    <i className="bi bi-check-circle me-1"></i>
                    Accept Order
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TruckerDashboard;
