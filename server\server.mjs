import express from 'express'
import cors from 'cors'
import 'dotenv/config'
import './db/conn.mjs'
import authRoutes from './routes/auth.js'
import purchaseOrderRoutes from './routes/purchaseOrders.js'

const PORT = process.env.PORT || 5051
const app = express()

app.use(cors())
app.use(express.json())

// Routes
app.use('/api/auth', authRoutes)
app.use('/api/purchase-orders', purchaseOrderRoutes)

// Health check endpoint
app.get('/api/ping', (req, res) => {
  res.json({ message: 'pong' });
});

// start the Express server
app.listen(PORT, () => {
  console.log(`Server is running on port: ${PORT}`)
})

